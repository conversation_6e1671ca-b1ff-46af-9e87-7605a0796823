#!/bin/bash

# 查询已存在用户信息的辅助脚本

WALLET_ADDRESS="$1"
CONFIG_DIR="$2"
PROXY_FILE="$3"

if [[ -z "$WALLET_ADDRESS" ]]; then
    echo "用法: $0 <wallet_address> [config_dir] [proxy_file]"
    exit 1
fi

echo "🔍 查询已存在用户信息: $WALLET_ADDRESS"

# 尝试不同的查询命令
if [[ -n "$PROXY_FILE" ]] && [[ -f "$PROXY_FILE" ]]; then
    echo "   使用代理配置: $PROXY_FILE"
    QUERY_OUTPUT=$(NEXUS_CONFIG_DIR="$CONFIG_DIR" NEXUS_PROXY_FILE="$PROXY_FILE" nexus-network query-user --wallet-address "$WALLET_ADDRESS" 2>&1)
else
    echo "   不使用代理"
    QUERY_OUTPUT=$(NEXUS_CONFIG_DIR="$CONFIG_DIR" nexus-network query-user --wallet-address "$WALLET_ADDRESS" 2>&1)
fi

echo "   查询输出: $QUERY_OUTPUT"

# 尝试从输出中提取用户ID
if [[ $QUERY_OUTPUT =~ User\ ID:\ ([a-f0-9\-]+) ]]; then
    USER_ID="${BASH_REMATCH[1]}"
    echo "✅ 找到用户ID: $USER_ID"
    echo "$USER_ID"
elif [[ $QUERY_OUTPUT =~ "user_id.*:.*\"([a-f0-9\-]+)\"" ]]; then
    USER_ID="${BASH_REMATCH[1]}"
    echo "✅ 找到用户ID: $USER_ID"
    echo "$USER_ID"
else
    echo "❌ 无法从查询结果中提取用户ID"
    # 生成一个基于钱包地址的临时ID
    USER_ID=$(echo -n "$WALLET_ADDRESS" | md5sum | cut -c1-8)
    USER_ID="$USER_ID-existing-user"
    echo "🔄 使用临时用户ID: $USER_ID"
    echo "$USER_ID"
fi
