# 🔧 Nexus Network 注册脚本调试指南

## 📋 问题总结

**主要问题**: `nexus-network` 命令不存在，导致 node id 注册一直失败。

## ✅ 解决方案

### 1. 安装 nexus-network 工具

你需要先安装 `nexus-network` 命令行工具。请参考 `install_guide.md` 文件中的安装方法。

### 2. 脚本改进

我已经对 `batch_register.sh` 脚本进行了以下改进：

- ✅ 添加了 `nexus-network` 命令存在性检查
- ✅ 增加了详细的调试输出信息
- ✅ 修复了 macOS 上 `flock` 命令不存在的问题
- ✅ 添加了更好的错误处理和提示

### 3. 调试工具

- `debug_nexus.sh` - 系统环境检查脚本
- `mock_nexus_network.sh` - 模拟 nexus-network 命令用于测试

## 🚀 使用步骤

### 步骤 1: 检查环境
```bash
./debug_nexus.sh
```

### 步骤 2: 准备钱包文件
确保 `wallets.txt` 文件格式正确：
```
******************************************,http://proxy1.example.com:8080
******************************************,http://proxy2.example.com:8080
******************************************,
```

### 步骤 3: 运行注册脚本
```bash
./batch_register.sh
```

### 步骤 4: 检查注册状态
```bash
./registration_status.sh
```

## 📊 输出文件

成功运行后，会生成 `wallet_user_node.csv` 文件，包含：
- wallet_address: 钱包地址
- user_id: 用户ID
- node_id: 节点ID
- proxy: 代理URL

## 🔍 常见问题

### Q: 脚本显示 "nexus-network 命令不存在"
**A**: 需要先安装 nexus-network 工具，参考 `install_guide.md`

### Q: 显示 "Wallet address already exists" (HTTP 409)
**A**: 这是正常情况！表示钱包已注册，脚本会自动处理：
- 尝试查询已存在的用户信息
- 继续注册节点
- 记录到输出文件中

### Q: 网络连接失败
**A**: 检查：
- 网络连接是否正常
- 代理配置是否正确
- nexus-network 服务是否可用

### Q: 钱包地址格式错误
**A**: 确保钱包地址格式为 `0x` 开头的40位十六进制字符

## 🧪 测试模式

如果想在安装真实 nexus-network 之前测试脚本，可以使用模拟模式：

```bash
# 设置模拟命令
export PATH="$HOME/bin:$PATH"

# 运行脚本
./batch_register.sh
```

## 📝 日志和调试

脚本现在会输出详细的调试信息，包括：
- 每次注册尝试的详细过程
- 命令的实际输出
- 失败原因分析
- 网络和代理状态

## 🔧 下一步

1. 安装真实的 `nexus-network` 工具
2. 准备真实的钱包地址和代理配置
3. 运行脚本进行实际注册
4. 监控输出文件和日志信息
