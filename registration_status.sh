#!/bin/bash

# 注册状态检查脚本

OUTPUT_FILE="wallet_user_node.csv"
WALLETS_FILE="wallets.txt"

echo "=== Nexus Network 注册状态报告 ==="
echo

# 检查输入文件
if [[ ! -f "$WALLETS_FILE" ]]; then
    echo "❌ 输入文件 $WALLETS_FILE 不存在"
    exit 1
fi

# 检查输出文件
if [[ ! -f "$OUTPUT_FILE" ]]; then
    echo "❌ 输出文件 $OUTPUT_FILE 不存在，请先运行注册脚本"
    exit 1
fi

# 统计信息
TOTAL_WALLETS=$(grep -v '^$' "$WALLETS_FILE" | wc -l)
TOTAL_REGISTRATIONS=$(tail -n +2 "$OUTPUT_FILE" | grep -v '^$' | wc -l)
UNIQUE_WALLETS=$(tail -n +2 "$OUTPUT_FILE" | cut -d',' -f1 | sort -u | wc -l)
UNIQUE_USERS=$(tail -n +2 "$OUTPUT_FILE" | cut -d',' -f2 | sort -u | wc -l)
TOTAL_NODES=$(tail -n +2 "$OUTPUT_FILE" | cut -d',' -f3 | grep -v '^$' | wc -l)

echo "📊 统计信息:"
echo "   总钱包数量: $TOTAL_WALLETS"
echo "   总注册记录: $TOTAL_REGISTRATIONS"
echo "   已注册钱包: $UNIQUE_WALLETS"
echo "   唯一用户数: $UNIQUE_USERS"
echo "   总节点数量: $TOTAL_NODES"

echo
echo "📋 详细状态:"

# 检查每个钱包的注册状态
while IFS=',' read -r wallet_address proxy_url; do
    [[ -z "$wallet_address" ]] && continue
    
    # 统计该钱包的注册情况
    WALLET_REGISTRATIONS=$(grep "^$wallet_address," "$OUTPUT_FILE" | wc -l)
    
    if [[ $WALLET_REGISTRATIONS -gt 0 ]]; then
        USER_ID=$(grep "^$wallet_address," "$OUTPUT_FILE" | head -1 | cut -d',' -f2)
        NODE_COUNT=$(grep "^$wallet_address," "$OUTPUT_FILE" | wc -l)
        echo "✅ $wallet_address"
        echo "   用户ID: $USER_ID"
        echo "   节点数量: $NODE_COUNT"
        echo "   代理: ${proxy_url:-无}"
    else
        echo "❌ $wallet_address"
        echo "   状态: 未注册"
        echo "   代理: ${proxy_url:-无}"
    fi
    echo
done < "$WALLETS_FILE"

echo "=== 报告完成 ==="

# 检查是否有失败的注册
FAILED_WALLETS=0
while IFS=',' read -r wallet_address proxy_url; do
    [[ -z "$wallet_address" ]] && continue
    if ! grep -q "^$wallet_address," "$OUTPUT_FILE"; then
        ((FAILED_WALLETS++))
    fi
done < "$WALLETS_FILE"

if [[ $FAILED_WALLETS -gt 0 ]]; then
    echo
    echo "⚠️  有 $FAILED_WALLETS 个钱包地址注册失败"
    echo "   建议重新运行注册脚本或检查网络连接"
fi
