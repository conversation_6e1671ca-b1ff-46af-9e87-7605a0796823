#!/bin/bash

WALLET_FILE="wallets.txt"  # 格式: wallet_address,proxy_url
OUTPUT_FILE="wallet_user_node.csv"
MAX_RETRIES=20
NODES_PER_ADDRESS=10
MAX_CONCURRENT=1                            # 现在可以并发了

> "$OUTPUT_FILE"
echo "wallet_address,user_id,node_id,proxy" >> "$OUTPUT_FILE"
LOCK_FILE="${OUTPUT_FILE}.lock"

# 注册单个钱包地址的函数
register_wallet() {
    local WALLET_ADDRESS="$1"
    local PROXY_URL="$2"
    local WALLET_HASH=$(echo -n "$WALLET_ADDRESS" | md5sum | cut -d' ' -f1)
    local CONFIG_DIR="$HOME/.nexus_${WALLET_HASH}"
    local PROXY_FILE="$CONFIG_DIR/proxy.txt"
    
    # 验证钱包地址格式
    if [[ ! "$WALLET_ADDRESS" =~ ^0x[a-fA-F0-9]{40}$ ]]; then
        echo "❌ 无效的钱包地址格式: $WALLET_ADDRESS"
        return
    fi
    
    # 创建独立配置目录
    mkdir -p "$CONFIG_DIR"
    
    # 保存代理配置到文件
    if [[ -n "$PROXY_URL" ]]; then
        echo "$PROXY_URL" > "$PROXY_FILE"
        echo "== 开始注册地址: $WALLET_ADDRESS (配置目录: $CONFIG_DIR, 代理: $PROXY_URL) =="
    else
        echo "== 开始注册地址: $WALLET_ADDRESS (配置目录: $CONFIG_DIR, 无代理) =="
    fi

    # --- 检查 nexus-network 命令是否存在 ---
    if ! command -v nexus-network >/dev/null 2>&1; then
        echo "❌ 错误: nexus-network 命令不存在！请先安装 nexus-network 工具"
        echo "   可能的解决方案："
        echo "   1. 检查是否已正确安装 nexus-network"
        echo "   2. 确认命令在 PATH 中"
        echo "   3. 检查安装文档获取正确的安装方法"
        return
    fi

    # --- 注册用户 ---
    local RETRIES=0
    local USER_ID=""
    while [ $RETRIES -lt $MAX_RETRIES ]; do
        echo "🔄 尝试注册用户 (第 $((RETRIES+1)) 次): $WALLET_ADDRESS"

        # 使用独立配置目录和代理配置
        if [[ -n "$PROXY_URL" ]]; then
            echo "   使用代理: $PROXY_URL"
            USER_OUTPUT=$(NEXUS_CONFIG_DIR="$CONFIG_DIR" NEXUS_PROXY_FILE="$PROXY_FILE" nexus-network register-user --wallet-address "$WALLET_ADDRESS" 2>&1)
        else
            echo "   不使用代理"
            USER_OUTPUT=$(NEXUS_CONFIG_DIR="$CONFIG_DIR" nexus-network register-user --wallet-address "$WALLET_ADDRESS" 2>&1)
        fi

        echo "   命令输出: $USER_OUTPUT"
        
        # 情况1: 新注册成功 - "User db9e26e7-1d0d-4682-94f1-a1cbf91b6c31 registered successfully."
        if [[ $USER_OUTPUT =~ User\ ([a-f0-9\-]+)\ registered\ successfully ]]; then
            USER_ID="${BASH_REMATCH[1]}"
            echo "✅ 用户注册成功: $USER_ID ($WALLET_ADDRESS)"
            break
        # 情况2: 已存在用户 - "User ID: db9e26e7-1d0d-4682-94f1-a1cbf91b6c31, Wallet Address: ..."
        elif [[ $USER_OUTPUT =~ "User already registered" ]] && [[ $USER_OUTPUT =~ User\ ID:\ ([a-f0-9\-]+) ]]; then
            USER_ID="${BASH_REMATCH[1]}"
            echo "✅ 用户已存在: $USER_ID ($WALLET_ADDRESS)"
            break
        else
            echo "❌ 用户注册失败（第 $((RETRIES+1)) 次）: $WALLET_ADDRESS"
            echo "   详细错误信息: $USER_OUTPUT"
            echo "   可能的原因："
            echo "   - 网络连接问题"
            echo "   - 代理配置错误"
            echo "   - nexus-network 服务不可用"
            echo "   - 钱包地址格式错误"
            ((RETRIES++))
            sleep 3
        fi
    done

    if [ $RETRIES -eq $MAX_RETRIES ]; then
        echo "⚠️ 跳过该地址，用户注册始终失败: $WALLET_ADDRESS"
        rm -rf "$CONFIG_DIR"
        return
    fi

    # --- 注册多个 Node ---
    local NODE_INDEX=1
    while [ $NODE_INDEX -le $NODES_PER_ADDRESS ]; do
        RETRIES=0
        while [ $RETRIES -lt $MAX_RETRIES ]; do
            echo "🔄 尝试注册 Node $NODE_INDEX (第 $((RETRIES+1)) 次): $WALLET_ADDRESS"

            if [[ -n "$PROXY_URL" ]]; then
                echo "   使用代理: $PROXY_URL"
                NODE_OUTPUT=$(NEXUS_CONFIG_DIR="$CONFIG_DIR" NEXUS_PROXY_FILE="$PROXY_FILE" nexus-network register-node 2>&1)
            else
                echo "   不使用代理"
                NODE_OUTPUT=$(NEXUS_CONFIG_DIR="$CONFIG_DIR" nexus-network register-node 2>&1)
            fi

            echo "   命令输出: $NODE_OUTPUT"
            
            if [[ $NODE_OUTPUT =~ Node\ registered\ successfully\ with\ ID:\ ([0-9]+) ]]; then
                NODE_ID="${BASH_REMATCH[1]}"
                echo "✅ Node $NODE_INDEX 注册成功: $NODE_ID ($WALLET_ADDRESS)"
                
                # 写入结果 (使用简单的文件锁定机制)
                while ! (set -C; echo "$WALLET_ADDRESS,$USER_ID,$NODE_ID,$PROXY_URL" >> "$OUTPUT_FILE") 2>/dev/null; do
                    sleep 0.1
                done
                break
            else
                echo "❌ Node $NODE_INDEX 注册失败（第 $((RETRIES+1)) 次）: $WALLET_ADDRESS"
                echo "   详细错误信息: $NODE_OUTPUT"
                echo "   可能的原因："
                echo "   - 用户ID无效或过期"
                echo "   - 网络连接问题"
                echo "   - nexus-network 服务不可用"
                ((RETRIES++))
                sleep 3
            fi
        done

        if [ $RETRIES -eq $MAX_RETRIES ]; then
            echo "⚠️ 跳过该 Node（始终失败）: $WALLET_ADDRESS (Node $NODE_INDEX)"
        fi

        ((NODE_INDEX++))
    done
    
    # 保留配置文件以备后用，或者删除临时配置
    # rm -rf "$CONFIG_DIR"
}

# 并发处理
concurrent_jobs=0
while IFS=',' read -r WALLET_ADDRESS PROXY_URL; do
    if [[ -z "$WALLET_ADDRESS" ]]; then
        continue
    fi
    
    # 去除可能的空格
    WALLET_ADDRESS=$(echo "$WALLET_ADDRESS" | xargs)
    PROXY_URL=$(echo "$PROXY_URL" | xargs)

    while [ $concurrent_jobs -ge $MAX_CONCURRENT ]; do
        wait -n
        ((concurrent_jobs--))
    done

    register_wallet "$WALLET_ADDRESS" "$PROXY_URL" &
    ((concurrent_jobs++))

done < "$WALLET_FILE"

wait
rm -f "$LOCK_FILE"

echo "🎉 全部完成，结果保存在 $OUTPUT_FILE"
