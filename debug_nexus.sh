#!/bin/bash

echo "=== Nexus Network 调试脚本 ==="
echo

# 1. 检查 nexus-network 命令
echo "1. 检查 nexus-network 命令是否存在..."
if command -v nexus-network >/dev/null 2>&1; then
    echo "✅ nexus-network 命令存在"
    echo "   位置: $(which nexus-network)"
    echo "   版本信息:"
    nexus-network --version 2>&1 || echo "   无法获取版本信息"
else
    echo "❌ nexus-network 命令不存在"
    echo
    echo "可能的解决方案："
    echo "1. 安装 nexus-network 工具"
    echo "2. 检查以下常见安装位置："
    echo "   - /usr/local/bin/nexus-network"
    echo "   - ~/.local/bin/nexus-network"
    echo "   - ~/bin/nexus-network"
    echo "3. 检查 PATH 环境变量是否包含安装目录"
    echo "   当前 PATH: $PATH"
fi

echo
echo "2. 检查可能的 nexus 相关文件..."
find /usr/local/bin /usr/bin ~/.local/bin ~/bin -name "*nexus*" 2>/dev/null | head -10 || echo "   未找到相关文件"

echo
echo "3. 检查环境变量..."
echo "   HOME: $HOME"
echo "   USER: $USER"
echo "   PWD: $PWD"

echo
echo "4. 检查网络连接..."
if ping -c 1 google.com >/dev/null 2>&1; then
    echo "✅ 网络连接正常"
else
    echo "❌ 网络连接可能有问题"
fi

echo
echo "5. 检查 wallets.txt 文件..."
if [[ -f "wallets.txt" ]]; then
    echo "✅ wallets.txt 文件存在"
    echo "   文件内容预览:"
    head -3 wallets.txt | while IFS=',' read -r wallet proxy; do
        echo "   钱包: $wallet, 代理: $proxy"
        # 验证钱包地址格式
        if [[ "$wallet" =~ ^0x[a-fA-F0-9]{40}$ ]]; then
            echo "     ✅ 钱包地址格式正确"
        else
            echo "     ❌ 钱包地址格式错误"
        fi
    done
else
    echo "❌ wallets.txt 文件不存在"
fi

echo
echo "6. 测试脚本权限..."
if [[ -x "batch_register.sh" ]]; then
    echo "✅ batch_register.sh 有执行权限"
else
    echo "❌ batch_register.sh 没有执行权限"
    echo "   运行: chmod +x batch_register.sh"
fi

echo
echo "=== 调试完成 ==="
echo
echo "如果 nexus-network 命令不存在，请："
echo "1. 查看项目文档了解如何安装 nexus-network"
echo "2. 或者提供正确的 nexus-network 可执行文件路径"
echo "3. 或者检查是否有其他名称的相关命令"
