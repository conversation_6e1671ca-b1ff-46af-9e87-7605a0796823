package main

import (
	"bytes"
	"encoding/csv"
	"fmt"
	"io"
	"log"
	"os"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"sync"
	"time"

	"golang.org/x/crypto/ssh"
)

const (
	maxRetries     = 10
	retryBackoff   = 2 * time.Second
	defaultSSHPort = 22
)

type Server struct {
	ID       string
	Host     string
	User     string
	Password string
	Port     int
	Address  string
	NodeID   string
}

func initLog() {
	logFile, err := os.OpenFile("batch_ssh.log", os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
	if err != nil {
		log.Fatalf("Failed to open log file: %v", err)
	}
	mw := io.MultiWriter(os.Stdout, logFile)
	log.SetOutput(mw)
	log.SetFlags(log.LstdFlags | log.Lshortfile)
}

func sshConfig(user, password string) *ssh.ClientConfig {
	return &ssh.ClientConfig{
		User:            user,
		Auth:            []ssh.AuthMethod{ssh.Password(password)},
		HostKeyCallback: ssh.InsecureIgnoreHostKey(),
		Timeout:         20 * time.Second,
	}
}

func executeCommand(server Server, command string) (string, error) {
	address := fmt.Sprintf("%s:%d", server.Host, server.Port)
	client, err := ssh.Dial("tcp", address, sshConfig(server.User, server.Password))
	if err != nil {
		return "", fmt.Errorf("failed to connect to %s: %w", server.Host, err)
	}
	defer client.Close()

	session, err := client.NewSession()
	if err != nil {
		return "", fmt.Errorf("failed to create session on %s: %w", server.Host, err)
	}
	defer session.Close()

	var stdout, stderr bytes.Buffer
	session.Stdout = &stdout
	session.Stderr = &stderr

	if err := session.Run(command); err != nil {
		return "", fmt.Errorf("command failed on %s: %v\nstderr: %s", server.Host, err, stderr.String())
	}
	return stdout.String(), nil
}

func batchExecuteCommand(servers []Server, command string, maxConcurrency int) {
	var wg sync.WaitGroup
	semaphore := make(chan struct{}, maxConcurrency)

	for _, server := range servers {
		wg.Add(1)
		go func(s Server) {
			defer wg.Done()
			semaphore <- struct{}{}
			defer func() { <-semaphore }()
			log.Printf("Executing command on %s...", s.Host)
			output, err := executeCommand(s, command)
			if err != nil {
				log.Printf("Error on %s: %v", s.Host, err)
			} else {
				log.Printf("Output from %s:\n%s", s.Host, output)
			}
		}(server)
	}

	wg.Wait()
	log.Println("Batch command execution completed.")
}

func uploadStringWithSSH(server Server, content, remotePath string) error {
	address := fmt.Sprintf("%s:%d", server.Host, server.Port)
	client, err := ssh.Dial("tcp", address, sshConfig(server.User, server.Password))
	if err != nil {
		return fmt.Errorf("connect failed: %w", err)
	}
	defer client.Close()

	session, err := client.NewSession()
	if err != nil {
		return fmt.Errorf("new session failed: %w", err)
	}
	defer session.Close()

	var stderr bytes.Buffer
	session.Stderr = &stderr

	w, err := session.StdinPipe()
	if err != nil {
		return fmt.Errorf("stdin pipe failed: %w", err)
	}

	go func() {
		defer w.Close()
		fmt.Fprintf(w, "C0644 %d %s\n", len(content), filepath.Base(remotePath))
		w.Write([]byte(content))
		fmt.Fprint(w, "\x00")
	}()

	if err := session.Run(fmt.Sprintf("scp -t %s", filepath.Dir(remotePath))); err != nil {
		return fmt.Errorf("scp run failed: %v\nstderr: %s", err, stderr.String())
	}
	return nil
}

func uploadStringWithRetry(server Server, content, remotePath string) error {
	var err error
	for i := 0; i < maxRetries; i++ {
		err = uploadStringWithSSH(server, content, remotePath)
		if err == nil {
			log.Printf("Upload to %s succeeded.", server.Host)
			return nil
		}
		log.Printf("Upload to %s failed (attempt %d/%d): %v", server.Host, i+1, maxRetries, err)
		time.Sleep(retryBackoff)
	}
	return err
}

func batchUploadString(servers []Server, remotePath string, content string, maxConcurrency int) {
	var wg sync.WaitGroup
	semaphore := make(chan struct{}, maxConcurrency)

	for _, server := range servers {
		wg.Add(1)
		go func(s Server) {
			defer wg.Done()
			semaphore <- struct{}{}
			defer func() { <-semaphore }()
			log.Printf("Uploading string to %s...", s.Host)
			if err := uploadStringWithRetry(s, content, remotePath); err != nil {
				log.Printf("Error uploading to %s: %v", s.Host, err)
			} else {
				log.Printf("Upload successful to %s", s.Host)
			}
		}(server)
	}
	wg.Wait()
	log.Println("Batch string upload completed.")
}

func loadServersFromCSV(filePath string) ([]Server, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return nil, fmt.Errorf("cannot open CSV: %w", err)
	}
	defer file.Close()

	reader := csv.NewReader(file)
	records, err := reader.ReadAll()
	if err != nil {
		return nil, fmt.Errorf("cannot read CSV: %w", err)
	}

	var servers []Server
	for i, r := range records {
		if len(r) < 4 {
			log.Printf("Skipping row %d: not enough fields", i+1)
			continue
		}
		port := defaultSSHPort
		if len(r) >= 5 && r[4] != "" {
			if p, err := strconv.Atoi(r[4]); err == nil {
				port = p
			}
		}
		address := ""
		if len(r) >= 6 {
			address = r[5]
		}
		nodeID := ""
		if len(r) >= 7 {
			nodeID = r[6]
		}
		servers = append(servers, Server{
			ID:       r[0],
			Host:     r[1],
			User:     r[2],
			Password: r[3],
			Port:     port,
			Address:  address,
			NodeID:   nodeID,
		})
	}
	return servers, nil
}

// batchRegister 批量注册 nexus 节点
// 通用重试执行命令
func executeCommandWithRetry(server Server, command string, maxRetries int, retryBackoff time.Duration) (string, error) {
	var err error
	var output string
	for i := 0; i < maxRetries; i++ {
		output, err = executeCommand(server, command)
		if err == nil {
			return output, nil
		}
		log.Printf("Retry %d/%d on %s: %v", i+1, maxRetries, server.Host, err)
		time.Sleep(retryBackoff)
	}
	return output, err
}

// batchRegister 批量注册 nexus 节点
func batchRegister(servers []Server, maxConcurrency int) {
	var wg sync.WaitGroup
	semaphore := make(chan struct{}, maxConcurrency)

	for _, server := range servers {
		wg.Add(1)
		go func(s Server) {
			defer wg.Done()
			semaphore <- struct{}{}
			defer func() { <-semaphore }()
			log.Printf("Executing command on %s...", s.Host)
			output, err := executeCommandWithRetry(s, fmt.Sprintf("/home/<USER>/.nexus/bin/nexus-network register-user --wallet-address %s", s.Address), maxRetries, retryBackoff)
			if err != nil {
				log.Printf("Error on %s after retry: %v", s.Host, err)
			} else {
				log.Printf("Output from %s:\n%s", s.Host, output)
			}
		}(server)
	}

	wg.Wait()
	log.Println("Batch command execution completed.")
}

// batchRegister 批量注册 nexus 节点
func batchRegisterNode(servers []Server, maxConcurrency int) {
	var wg sync.WaitGroup
	semaphore := make(chan struct{}, maxConcurrency)

	for _, server := range servers {
		wg.Add(1)
		go func(s Server) {
			defer wg.Done()
			semaphore <- struct{}{}
			defer func() { <-semaphore }()
			log.Printf("Executing command on %s...", s.Host)
			output, err := executeCommandWithRetry(s, "/home/<USER>/.nexus/bin/nexus-network register-node", maxRetries, retryBackoff)
			if err != nil {
				log.Printf("Error on %s after retry: %v", s.Host, err)
			} else {
				log.Printf("Output from %s:\n%s", s.Host, output)
			}
		}(server)
	}

	wg.Wait()
	log.Println("Batch command execution completed.")
}

func startNexusInScreen(servers []Server, maxConcurrency int) {
	var wg sync.WaitGroup
	semaphore := make(chan struct{}, maxConcurrency)

	for _, server := range servers {
		wg.Add(1)
		go func(s Server) {
			defer wg.Done()
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			log.Printf("Starting Nexus in screen on %s...", s.Host)

			// 创建一个已分离的screen会话并在其中启动nexus
			cmd := "screen -dmS nexus /home/<USER>/.nexus/bin/nexus-network start --node-id " + s.NodeID

			output, err := executeCommand(s, cmd)
			if err != nil {
				log.Printf("Error on %s: %v", s.Host, err)
			} else {
				log.Printf("Successfully started Nexus in screen on %s: %s", s.Host, output)
			}
		}(server)
	}

	wg.Wait()
	log.Println("Nexus startup completed on all servers.")
}

func executeInteractiveCommand(server Server, command string) error {
	address := fmt.Sprintf("%s:%d", server.Host, server.Port)
	client, err := ssh.Dial("tcp", address, sshConfig(server.User, server.Password))
	if err != nil {
		return fmt.Errorf("failed to connect to %s: %w", server.Host, err)
	}
	defer client.Close()

	session, err := client.NewSession()
	if err != nil {
		return fmt.Errorf("failed to create session on %s: %w", server.Host, err)
	}
	defer session.Close()

	// 请求伪终端
	modes := ssh.TerminalModes{
		ssh.ECHO:          1,
		ssh.TTY_OP_ISPEED: 14400,
		ssh.TTY_OP_OSPEED: 14400,
	}
	if err := session.RequestPty("xterm", 80, 40, modes); err != nil {
		return fmt.Errorf("request for pseudo terminal failed: %w", err)
	}

	// 设置标准输入输出
	session.Stdout = os.Stdout
	session.Stderr = os.Stderr
	session.Stdin = os.Stdin

	// 启动命令
	if err := session.Start(command); err != nil {
		return fmt.Errorf("failed to start command: %w", err)
	}

	// 等待命令完成
	if err := session.Wait(); err != nil {
		return fmt.Errorf("command execution failed: %w", err)
	}

	return nil
}

// 添加带重试的批量执行命令函数
func batchExecuteCommandWithRetry(servers []Server, command string, maxRetries int, retryBackoff time.Duration, maxConcurrency int) {
	var wg sync.WaitGroup
	semaphore := make(chan struct{}, maxConcurrency)

	for _, server := range servers {
		wg.Add(1)
		go func(s Server) {
			defer wg.Done()
			semaphore <- struct{}{}
			defer func() { <-semaphore }()
			log.Printf("Executing command on %s...", s.Host)
			output, err := executeCommandWithRetry(s, command, maxRetries, retryBackoff)
			if err != nil {
				log.Printf("Error on %s after all retries: %v", s.Host, err)
			} else {
				log.Printf("Output from %s:\n%s", s.Host, output)
			}
		}(server)
	}

	wg.Wait()
	log.Println("Batch command execution with retry completed.")
}

func batchUpdatePassword(servers []Server, newPassword string, maxConcurrency int) {
	var wg sync.WaitGroup
	semaphore := make(chan struct{}, maxConcurrency)

	for _, server := range servers {
		wg.Add(1)
		go func(s Server) {
			defer wg.Done()
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			log.Printf("正在更新 %s 的密码...", s.Host)

			// 使用更可靠的方式更新密码 - chpasswd 命令
			cmd := fmt.Sprintf(`echo '%s' | sudo -S sh -c "echo '%s:%s' | chpasswd"`,
				s.Password, s.User, newPassword)

			// 方法1：使用下划线忽略不需要的返回值
			_, err := executeCommand(s, cmd)
			if err != nil {
				log.Printf("在 %s 上更新密码失败: %v", s.Host, err)
			} else {
				// 验证密码是否成功更新
				verifyCmd := fmt.Sprintf(`echo '%s' | sudo -S echo "密码验证成功"`, newPassword)
				verifyOutput, verifyErr := executeCommand(s, verifyCmd)
				if verifyErr != nil {
					log.Printf("在 %s 上密码更新验证失败: %v", s.Host, verifyErr)
				} else {
					log.Printf("在 %s 上密码已更新并验证成功: %s", s.Host, verifyOutput)
				}
			}
		}(server)
	}

	wg.Wait()
	log.Println("批量密码更新完成。")
}

func updatePasswordsInCSV(filePath string, servers []Server, newPassword string) error {
	file, err := os.Create(filePath)
	if err != nil {
		return fmt.Errorf("failed to open CSV file: %w", err)
	}
	defer file.Close()

	writer := csv.NewWriter(file)
	defer writer.Flush()

	for _, server := range servers {
		// 更新密码字段
		record := []string{
			server.ID,
			server.Host,
			server.User,
			newPassword,
			strconv.Itoa(server.Port),
			server.Address,
			server.NodeID,
		}
		if err := writer.Write(record); err != nil {
			return fmt.Errorf("error writing record to CSV: %w", err)
		}
	}

	return nil
}

func deploySSHKey(servers []Server, pubKeyContent string, maxConcurrency int) {
	var wg sync.WaitGroup
	semaphore := make(chan struct{}, maxConcurrency)

	for _, server := range servers {
		wg.Add(1)
		go func(s Server) {
			defer wg.Done()
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			log.Printf("Deploying SSH key to %s...", s.Host)

			// 创建.ssh目录并设置权限
			setupCmd := `
                mkdir -p ~/.ssh
                chmod 700 ~/.ssh
                touch ~/.ssh/authorized_keys
                chmod 600 ~/.ssh/authorized_keys
            `
			_, err := executeCommand(s, setupCmd)
			if err != nil {
				log.Printf("Failed to setup .ssh directory on %s: %v", s.Host, err)
				return
			}

			// 添加公钥到authorized_keys，避免重复添加
			checkCmd := fmt.Sprintf(`grep -q "%s" ~/.ssh/authorized_keys || echo "%s" >> ~/.ssh/authorized_keys`,
				pubKeyContent, pubKeyContent)
			_, err = executeCommand(s, checkCmd)
			if err != nil {
				log.Printf("Failed to add SSH key to %s: %v", s.Host, err)
				return
			}

			log.Printf("SSH key successfully deployed to %s", s.Host)
		}(server)
	}

	wg.Wait()
	log.Println("SSH key deployment completed.")
}

func uploadLocalFileToRemote(servers []Server, localFilePath, remoteFilePath string, maxConcurrency int) {
	var wg sync.WaitGroup
	semaphore := make(chan struct{}, maxConcurrency)

	// Read the local file content
	content, err := os.ReadFile(localFilePath)
	if err != nil {
		log.Printf("Failed to read local file %s: %v", localFilePath, err)
		return
	}

	for _, server := range servers {
		wg.Add(1)
		go func(s Server) {
			defer wg.Done()
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			log.Printf("Uploading %s to %s on %s...", localFilePath, remoteFilePath, s.Host)
			if err := uploadStringWithRetry(s, string(content), remoteFilePath); err != nil {
				log.Printf("Error uploading to %s: %v", s.Host, err)
			} else {
				log.Printf("Successfully uploaded %s to %s on %s", localFilePath, remoteFilePath, s.Host)
			}
		}(server)
	}

	wg.Wait()
	log.Println("File upload completed to all servers.")
}

func uploadUserData(servers []Server, maxConcurrency int) {
	var wg sync.WaitGroup
	semaphore := make(chan struct{}, maxConcurrency)

	for _, server := range servers {
		wg.Add(1)
		go func(s Server) {
			defer wg.Done()
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			// Read the local file content
			userData := fmt.Sprintf("./temp-data/%s/userData.json", s.Address)
			content, err := os.ReadFile(userData)
			if err != nil {
				log.Printf("Failed to read local file %s: %v", userData, err)
				return
			}
			log.Printf("Uploading %s to %s...", userData, s.Host)
			if err := uploadStringWithRetry(s, string(content), "~/gensyn/rl-swarm/modal-login/temp-data/userData.json"); err != nil {
				log.Printf("Error uploading to %s: %v", s.Host, err)
			} else {
				log.Printf("Successfully uploaded %s to %s on %s", userData, "~/gensyn/rl-swarm/modal-login/temp-data/userData.json", s.Host)
			}

			userApiKey := fmt.Sprintf("./temp-data/%s/userApiKey.json", s.Address)
			userApiKeyContent, err := os.ReadFile(userApiKey)
			if err != nil {
				log.Printf("Failed to read local file %s: %v", userApiKey, err)
				return
			}
			log.Printf("Uploading %s to %s on %s...", userApiKey, "~/gensyn/rl-swarm/modal-login/temp-data/userApiKey.json", s.Host)
			if err := uploadStringWithRetry(s, string(userApiKeyContent), "~/gensyn/rl-swarm/modal-login/temp-data/userApiKey.json"); err != nil {
				log.Printf("Error uploading to %s: %v", s.Host, err)
			} else {
				log.Printf("Successfully uploaded %s to %s on %s", userApiKey, "~/gensyn/rl-swarm/modal-login/temp-data/userApiKey.json", s.Host)
			}
		}(server)
	}

	wg.Wait()
	log.Println("File upload completed to all servers.")
}

func uploadSwamPem(servers []Server, maxConcurrency int) {
	var wg sync.WaitGroup
	semaphore := make(chan struct{}, maxConcurrency)

	for _, server := range servers {
		wg.Add(1)
		go func(s Server) {
			defer wg.Done()
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			// Read the local file content
			userData := fmt.Sprintf("./swarms/%s-swarm.pem", s.Address)
			content, err := os.ReadFile(userData)
			if err != nil {
				log.Printf("Failed to read local file %s: %v", userData, err)
				return
			}
			log.Printf("Uploading %s to %s...", userData, s.Host)
			if err := uploadStringWithRetry(s, string(content), "~/gensyn/rl-swarm/swarm.pem"); err != nil {
				log.Printf("Error uploading to %s: %v", s.Host, err)
			} else {
				log.Printf("Successfully uploaded %s to %s on %s", userData, "~/gensyn/rl-swarm/swarm.pem", s.Host)
			}
		}(server)
	}

	wg.Wait()
	log.Println("File upload completed to all servers.")
}

func extractPeerInfoFromLogs(server Server) (string, string, error) {
	// Execute command to get the log content
	output, err := executeCommand(server, "tail -n 500 ~/gensyn/rl-swarm/logs/swarm_launcher.log")
	if err != nil {
		return "", "", fmt.Errorf("failed to read logs on %s: %w", server.Host, err)
	}

	// Regular expression to extract the animal name and peer ID
	// Matches patterns like: 🐱 Hello 🐈 [sharp grunting alpaca] 🦮 [QmT6agz852aNcVvupWv3kA2Xh7Ahmy6CrXUbuZA3y1JpQ5]
	re := regexp.MustCompile(`Hello 🐈 \[(.*?)\] 🦮 \[(Qm[a-zA-Z0-9]+)\]`)
	matches := re.FindStringSubmatch(output)

	if len(matches) < 3 {
		return "", "", fmt.Errorf("could not find peer info in logs on %s", server.Host)
	}

	animalName := matches[1]
	peerID := matches[2]

	return animalName, peerID, nil
}

func collectPeerInfoFromAllServers(servers []Server, maxConcurrency int) []map[string]string {
	var wg sync.WaitGroup
	semaphore := make(chan struct{}, maxConcurrency)

	// Use mutex to protect the shared result slice
	var mu sync.Mutex
	results := make([]map[string]string, 0, len(servers))

	for _, server := range servers {
		wg.Add(1)
		go func(s Server) {
			defer wg.Done()
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			log.Printf("Extracting peer info from %s...", s.Host)
			animalName, peerID, err := extractPeerInfoFromLogs(s)

			if err != nil {
				log.Printf("Error extracting peer info from %s: %v", s.Host, err)
				return
			}

			// Add the result to our collection
			mu.Lock()
			results = append(results, map[string]string{
				"host":       s.Host,
				"email":      s.Address,
				"animalName": animalName,
				"peerID":     peerID,
			})
			mu.Unlock()

			log.Printf("Found peer info on %s: %s (%s)", s.Host, animalName, peerID)
		}(server)
	}

	wg.Wait()
	log.Printf("Collected peer info from %d servers", len(results))
	return results
}

// SavePeerInfoToCSV saves the collected peer information to a CSV file
func savePeerInfoToCSV(peerInfo []map[string]string, filePath string) error {
	file, err := os.Create(filePath)
	if err != nil {
		return fmt.Errorf("failed to create CSV file: %w", err)
	}
	defer file.Close()

	writer := csv.NewWriter(file)
	defer writer.Flush()

	// Write header
	if err := writer.Write([]string{"Host", "Email", "AnimalName", "PeerID"}); err != nil {
		return fmt.Errorf("error writing CSV header: %w", err)
	}

	// Write data
	for _, info := range peerInfo {
		record := []string{
			info["host"],
			info["email"],
			info["animalName"],
			info["peerID"],
		}
		if err := writer.Write(record); err != nil {
			return fmt.Errorf("error writing record to CSV: %w", err)
		}
	}

	return nil
}

func downloadSwarmPemFiles(servers []Server, maxConcurrency int) {
	var wg sync.WaitGroup
	semaphore := make(chan struct{}, maxConcurrency)

	// Create directory for storing the pem files if it doesn't exist
	if err := os.MkdirAll("./swarms", 0755); err != nil {
		log.Printf("Failed to create directory for pem files: %v", err)
		return
	}

	for _, server := range servers {
		wg.Add(1)
		go func(s Server) {
			defer wg.Done()
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			// Skip servers without an address
			if s.Address == "" {
				log.Printf("Skipping server %s: no address specified", s.Host)
				return
			}

			log.Printf("Downloading swarm.pem from %s...", s.Host)

			// Execute command to get the file content
			output, err := executeCommand(s, "cat ~/gensyn/rl-swarm/swarm.pem")
			if err != nil {
				log.Printf("Failed to download swarm.pem from %s: %v", s.Host, err)

				// Try alternative path if the first one fails
				output, err = executeCommand(s, "cat ~/gensyn/swarm.pem")
				if err != nil {
					log.Printf("Failed to download swarm.pem from alternative path on %s: %v", s.Host, err)
					return
				}
			}

			// Save the file locally with the server's address in the filename
			filename := fmt.Sprintf("./swarms/%s-swarm.pem", s.Address)
			if err := os.WriteFile(filename, []byte(output), 0600); err != nil {
				log.Printf("Failed to save swarm.pem from %s: %v", s.Host, err)
				return
			}

			log.Printf("Successfully downloaded and saved swarm.pem from %s to %s", s.Host, filename)
		}(server)
	}

	wg.Wait()
	log.Println("Download of swarm.pem files completed.")
}

// 添加一个函数来验证screen会话是否运行
func verifyScreenSession(server Server, sessionName string) (bool, error) {
	cmd := fmt.Sprintf("screen -list | grep %s", sessionName)
	output, err := executeCommand(server, cmd)
	if err != nil {
		return false, err
	}
	return strings.Contains(output, sessionName), nil
}

// 更全面的验证，检查进程是否运行
func verifyGensynRunning(server Server) (bool, error) {
	cmd := "ps aux | grep -v grep | grep 'python -m rgym_exp.runner.swarm_launcher'"
	output, err := executeCommand(server, cmd)
	if err != nil {
		// 命令可能因为grep没有找到匹配而返回非零状态
		return false, nil
	}
	return output != "", nil
}

// 增强的批量执行命令函数，添加更多诊断和重试逻辑
func enhancedBatchExecuteWithVerification(servers []Server, command string, sessionName string, maxRetries int, retryBackoff time.Duration, maxConcurrency int) {
	var wg sync.WaitGroup
	semaphore := make(chan struct{}, maxConcurrency)

	for _, server := range servers {
		wg.Add(1)
		go func(s Server) {
			defer wg.Done()
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			log.Printf("Executing command on %s...", s.Host)

			// 1. 首先检查screen是否已安装
			screenCheck, err := executeCommand(s, "which screen || echo 'NOT_INSTALLED'")
			if err != nil || strings.Contains(screenCheck, "NOT_INSTALLED") {
				log.Printf("Screen not installed on %s, installing...", s.Host)
				_, err := executeCommand(s, "sudo apt-get update && sudo apt-get install -y screen")
				if err != nil {
					log.Printf("Failed to install screen on %s: %v", s.Host, err)
					return
				}
			}

			// 2. 清理可能存在的僵尸screen会话
			_, _ = executeCommand(s, fmt.Sprintf("screen -wipe >/dev/null 2>&1 || true"))

			// 3. 确保目录存在
			dirCheck := strings.Split(command, "cd ")[1]
			dirPath := strings.Split(dirCheck, " ")[0]
			_, err = executeCommand(s, fmt.Sprintf("mkdir -p %s", dirPath))
			if err != nil {
				log.Printf("Failed to create directory on %s: %v", s.Host, err)
			}

			// 4. 检查是否已经在运行
			isAlreadyRunning, _ := verifyScreenSession(s, sessionName)
			if isAlreadyRunning {
				log.Printf("✅ Screen session %s already running on %s, skipping", sessionName, s.Host)
				return
			}

			// 或者检查进程是否在运行
			isProcessRunning, _ := verifyGensynRunning(s)
			if isProcessRunning {
				log.Printf("✅ Gensyn process already running on %s, skipping", s.Host)
				return
			}

			// 5. 执行主命令
			_, err = executeCommandWithRetry(s, command, maxRetries, retryBackoff)
			if err != nil {
				log.Printf("Error on %s after all retries: %v", s.Host, err)
				return
			}

			// 6. 验证screen会话是否成功启动
			time.Sleep(3 * time.Second) // 给screen启动更多时间
			isRunning, err := verifyScreenSession(s, sessionName)
			if err != nil || !isRunning {
				log.Printf("Screen session %s verification failed on %s: %v", sessionName, s.Host, err)

				// 7. 如果失败，尝试直接启动而不使用screen
				log.Printf("Trying alternative approach on %s...", s.Host)
				altCommand := strings.Replace(command, "screen -dmS "+sessionName, "nohup", 1) + " > ~/gensyn.log 2>&1 &"
				_, err = executeCommand(s, altCommand)
				if err != nil {
					log.Printf("Alternative approach also failed on %s: %v", s.Host, err)
				} else {
					// 验证进程是否在运行
					time.Sleep(3 * time.Second)
					isRunning, _ := verifyGensynRunning(s)
					if isRunning {
						log.Printf("✅ Successfully started process on %s using alternative approach", s.Host)
					} else {
						log.Printf("❌ Failed to start process on %s using both approaches", s.Host)
					}
				}
			} else {
				log.Printf("✅ Successfully verified screen session %s is running on %s", sessionName, s.Host)
			}
		}(server)
	}

	wg.Wait()
	log.Println("Enhanced batch command execution completed.")
}

// 清理并重启gensyn服务
func cleanupAndRestartGensyn(servers []Server, maxRetries int, retryBackoff time.Duration, maxConcurrency int) {
	log.Println("Cleaning up existing gensyn screen sessions...")

	// 使用更简单的清理方式，不使用重试机制避免SIGTERM错误
	batchExecuteCommand(servers, "pkill -f 'screen.*gensyn' || true", maxConcurrency)
	time.Sleep(2 * time.Second)

	batchExecuteCommand(servers, "pkill -f 'gensyn/run.sh' || true", maxConcurrency)
	time.Sleep(2 * time.Second)

	batchExecuteCommand(servers, "pkill -f 'python -m hivemind_exp' || true", maxConcurrency)
	time.Sleep(3 * time.Second)

	log.Println("Starting fresh gensyn instances...")

	// 重新启动
	enhancedBatchExecuteWithVerification(servers, "cd ~/gensyn && screen -dmS gensyn ~/gensyn/run.sh", "gensyn", maxRetries, retryBackoff, maxConcurrency)
}

func main() {
	initLog()

	servers, err := loadServersFromCSV("./servers.csv")
	if err != nil {
		log.Fatalf("Load server list failed: %v", err)
	}

	// pubKeyBytes, err := os.ReadFile("/Users/<USER>/.ssh/id_rsa.pub") // 或者使用你的公钥路径
	// if err != nil {
	// 	log.Fatalf("Failed to read SSH public key: %v", err)
	// }
	// pubKeyContent := string(pubKeyBytes)

	// // 部署SSH公钥到所有服务器
	// deploySSHKey(servers, pubKeyContent, 30)

	// // 批量更新密码
	// newPassword := "bsdb!@#$%zQzb019dee" // 替换为你想要设置的新密码
	// batchUpdatePassword(servers, newPassword, 30)

	// // 在验证成功后更新服务器对象中的密码
	// for i := range servers {
	// 	servers[i].Password = newPassword
	// }

	// // 更新CSV文件中的密码
	// if err := updatePasswordsInCSV("./servers.csv", servers, newPassword); err != nil {
	// 	log.Printf("Warning: Failed to update CSV file: %v", err)
	// } else {
	// 	log.Println("CSV file updated with new passwords.")
	// }

	// 检查 Docker 是否已安装，未安装才执行安装命令
	// command := `
	// if ! command -v docker &> /dev/null; then
	//   sudo apt-get update && \
	//   sudo apt-get install -y ca-certificates curl && \
	//   sudo install -m 0755 -d /etc/apt/keyrings && \
	//   sudo curl -fsSL https://download.docker.com/linux/ubuntu/gpg -o /etc/apt/keyrings/docker.asc && \
	//   sudo chmod a+r /etc/apt/keyrings/docker.asc && \
	//   echo "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.asc] https://download.docker.com/linux/ubuntu $(. /etc/os-release && echo \"${UBUNTU_CODENAME:-$VERSION_CODENAME}\") stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null && \
	//   sudo apt-get update && \
	//   sudo apt-get install -y docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin
	// fi`

	// command := "sudo docker stop socks5 && sudo docker rm socks5 && sudo docker run -d --name socks5 -p 22222:1080 -e PROXY_USER=nexus -e PROXY_PASSWORD=9oe0hY5o4k2dKlCcFb serjs/go-socks5-proxy"
	// batchExecuteCommandWithRetry(servers, command, maxRetries, retryBackoff, 30)

	// 注册 nexus 地址
	// batchRegister(servers, 20)
	// 注册 nexus 节点
	// batchRegisterNode(servers, 30)

	// 使用重试机制执行reboot命令
	// batchExecuteCommandWithRetry(servers, "sudo reboot", maxRetries, retryBackoff, 30)
	// batchExecuteCommandWithRetry(servers, "sudo rm -rf /home/<USER>/.nexus/bin/ && bash -lc 'curl https://raw.githubusercontent.com/TheLuckyVince/nexus-cli/main/install.sh | sh'", maxRetries, retryBackoff, 30)
	// batchExecuteCommandWithRetry(servers, "sudo rm -rf /home/<USER>/.nexus/bin/ && bash -lc 'curl https://cli.nexus.xyz/ | sh'", maxRetries, retryBackoff, 30)
	// 启动Nexus服务
	// startNexusInScreen(servers, 30)

	// 示例：执行 leader 和 follower 启动命令（使用 nohup）
	// leaderCmd := "cd ~/nockchain && bash -lc 'nohup make run-nockchain-leader > leader.log 2>&1 &'"
	// followerCmd := "cd ~/nockchain && bash -lc 'nohup make run-nockchain-follower > follower.log 2>&1 &'"
	// batchExecuteCommand(servers, leaderCmd, 30)
	// batchExecuteCommand(servers, followerCmd, 30)

	// cmd := "mv ~/keys.export ~/nockchain/keys.export && cd ~/nockchain && bash -lc 'nockchain-wallet import-keys --input keys.export'"
	// cmd := "cd ~/nockchain && bash -lc 'nockchain-wallet import-keys --input keys.export'"
	// cmd := "cd ~/nockchain && bash -lc 'nohup bash ./scripts/run_nockchain_miner.sh > run.log 2>&1 &'"
	// cmd := "sudo rm -rf ~/nockchain/.socket"
	// cmd := `ps aux | grep "nockchain --mining-pubkey"`
	// cmd := `echo 'vm.overcommit_memory=1' | sudo tee -a /etc/sysctl.conf && sudo sysctl -p`
	// cmd := `sudo chmod +x ~/nockchain/scripts/monitor_nockchain.sh && nohup ~/nockchain/scripts/monitor_nockchain.sh > monitor.log 2>&1 &`
	// cmd := `sed -i 's|^MINING_PUBKEY=.*|MINING_PUBKEY=35ekEE2hkzTAJS99i7c4Qb2jWd2XKLjFH8mxkS8AmvfTpGjzS5fTJx1L8JWiXSLXxpawMCNdJH2KSfxYxWGgD7kGKG6sb47EowYKYL69ScsDEEkypMT4TLhVANx3NJPgD1cC|' ~/nockchain/.env`

	// batchExecuteCommand(servers, "sudo rm -rf ~/gensyn", 10)
	// batchExecuteCommand(servers, "sudo rm -rf ~/gensyn*", 10)
	// batchExecuteCommand(servers, "sudo rm -rf ~/__MACOSX", 10)

	// 下载GENSYN
	// batchExecuteCommandWithRetry(servers, "wget https://dva.okvlink.com/files/gensyn.zip && unzip gensyn.zip && rm gensyn.zip", maxRetries, retryBackoff, 5)

	// 替换远程的run.sh文件
	// uploadLocalFileToRemote(servers, "./run.sh", "~/gensyn/run.sh", 10)
	// 确保run.sh有执行权限
	// batchExecuteCommandWithRetry(servers, "chmod +x ~/gensyn/run.sh", maxRetries, retryBackoff, 10)

	// batchExecuteCommand(servers, "sudo rm -rf ~/gensyn/rl-swarm/logs/*", 30)
	// batchExecuteCommand(servers, "sudo rm -rf ~/__MACOSX", 30)

	// 上传userData
	// uploadUserData(servers, 10)
	// 上传swarm
	// uploadSwamPem(servers, 5)
	// 使用增强版的函数运行GENSYN
	// enhancedBatchExecuteWithVerification(servers, "cd ~/gensyn && screen -dmS gensyn ~/gensyn/run.sh", "gensyn", maxRetries, retryBackoff, 20)

	// cmd := "tail -n 100 ~/gensyn/rl-swarm/logs/swarm_launcher.log"
	// batchExecuteCommand(servers, cmd, 30)

	// batchExecuteCommand(servers, "sudo rm -rf ~/gensyn/rl-swarm/swarm.pem && sudo rm -rf ~/gensyn/rl-swarm/swarm.json", 30)
	// batchExecuteCommand(servers, "sudo rm -rf ~/gensyn/rl-swarm/modal-login/.next", 30)

	// 收集所有服务器的节点信息
	// peerInfo := collectPeerInfoFromAllServers(servers, 10)
	// // 保存到CSV文件
	// if err := savePeerInfoToCSV(peerInfo, "peer_info.csv"); err != nil {
	// 	log.Printf("Failed to save peer info to CSV: %v", err)
	// } else {
	// 	log.Println("Peer information saved to peer_info.csv")
	// }

	// // 下载swarm.pem文件
	downloadSwarmPemFiles(servers, 30)
}
