# Nexus Network 安装指南

## 问题
`nexus-network` 命令不存在，导致注册脚本失败。

## 可能的安装方法

### 方法1: 从官方源安装
```bash
# 检查是否有官方安装脚本
curl -sSL https://nexus.xyz/install.sh | bash

# 或者使用 npm (如果是 Node.js 包)
npm install -g nexus-network

# 或者使用 pip (如果是 Python 包)
pip install nexus-network
```

### 方法2: 从 GitHub 下载
```bash
# 查找官方 GitHub 仓库
# 通常格式为: https://github.com/nexus-xyz/nexus-network
# 下载最新版本的二进制文件

# 示例 (需要替换为实际的下载链接):
wget https://github.com/nexus-xyz/nexus-network/releases/latest/download/nexus-network-macos
chmod +x nexus-network-macos
sudo mv nexus-network-macos /usr/local/bin/nexus-network
```

### 方法3: 使用包管理器
```bash
# macOS (Homebrew)
brew install nexus-network

# Ubuntu/Debian
sudo apt update
sudo apt install nexus-network

# CentOS/RHEL
sudo yum install nexus-network
```

### 方法4: 从源码编译
```bash
# 如果是 Go 项目
git clone https://github.com/nexus-xyz/nexus-network.git
cd nexus-network
go build -o nexus-network
sudo mv nexus-network /usr/local/bin/
```

## 验证安装
安装完成后，运行以下命令验证：
```bash
nexus-network --version
nexus-network --help
```

## 下一步
安装完成后，重新运行注册脚本：
```bash
./batch_register.sh
```
