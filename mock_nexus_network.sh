#!/bin/bash

# 模拟 nexus-network 命令用于测试
# 这个脚本模拟真实的 nexus-network 行为

case "$1" in
    "register-user")
        # 模拟用户注册
        WALLET_ADDRESS=""
        for arg in "$@"; do
            if [[ "$arg" =~ ^0x[a-fA-F0-9]{40}$ ]]; then
                WALLET_ADDRESS="$arg"
                break
            fi
        done
        
        if [[ -n "$WALLET_ADDRESS" ]]; then
            # 生成模拟的用户ID
            USER_ID=$(echo -n "$WALLET_ADDRESS" | md5sum | cut -c1-8)
            USER_ID="$USER_ID-1234-5678-9abc-def012345678"
            echo "User $USER_ID registered successfully."
        else
            echo "Error: Invalid wallet address"
            exit 1
        fi
        ;;
        
    "register-node")
        # 模拟节点注册
        # 生成随机的节点ID
        NODE_ID=$((RANDOM % 9000 + 1000))
        echo "Node registered successfully with ID: $NODE_ID"
        ;;
        
    "--version")
        echo "nexus-network version 1.0.0 (mock)"
        ;;
        
    "--help")
        echo "Mock nexus-network command"
        echo "Usage:"
        echo "  nexus-network register-user --wallet-address <address>"
        echo "  nexus-network register-node"
        echo "  nexus-network --version"
        echo "  nexus-network --help"
        ;;
        
    *)
        echo "Unknown command: $1"
        echo "Use --help for usage information"
        exit 1
        ;;
esac
